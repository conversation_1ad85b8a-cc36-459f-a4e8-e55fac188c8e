document.addEventListener('DOMContentLoaded', function() {
    // Quiz questions
    const questions = [
        {
            question: "1. What's your reaction to a green 200% candle?",
            options: [
                { text: "A. \"JEET\"", score: 1 },
                { text: "B. \"Nice! Time to take out initials.\"", score: 2 },
                { text: "<PERSON>. \"I'll just watch it.\"", score: 3 },
                 { text: "D. \"Buy more. Momentum is everything.\"", score: 4 }
            ]
        },
        {
            question: "2. How many times have you bought a token based solely on the name or logo?",
            options: [
                { text: "A. 0", score: 1 },
                { text: "B. 1–2", score: 2 },
                { text: "C. 3–5", score: 3 },
                { text: "D. Lost count", score: 4 }
            ]
        },
        {
            question: "3. What's your biggest single loss on a trade?",
            options: [
                { text: "A. I always use stop-losses.", score: 1 },
                { text: "B. A few hundred bucks.", score: 2 },
                { text: "C. A few thousand.", score: 3 },
                { text: "D. My rent. Twice.", score: 4 }
            ]
        },
        {
            question: "4. You find a token named $PUMP69 on Dexscreener with 12 holders. What do you do?",
            options: [
                { text: "A. Ignore it, clearly a scam.", score: 1 },
                { text: "B. DYOR first.", score: 2 },
                { text: "C. Throw 0.1 SOL in just in case.", score: 3 },
                { text: "D. Max wallet. Tweet \"We're going to Valhalla.\"", score: 4 }
            ]
        },
        {
            question: "5. Have you ever clicked a phishing link?",
            options: [
                { text: "A. Nope.", score: 1 },
                { text: "B. Almost.", score: 2 },
                { text: "C. Yes — my memes got drained, but I came back stronger.", score: 3 },
                { text: "D. I lost three wallets and still haven't learned.", score: 4 }
            ]
        },
        {
            question: "6. Do you have a burner wallet?",
            options: [
                { text: "A. Yes, with multisig + cold storage.", score: 1 },
                { text: "B. One or two.", score: 2 },
                { text: "C. I keep everything in my main wallet.", score: 3 },
                { text: "D. What's a burner wallet?", score: 4 }
            ]
        },
        {
            question: "7. How many rugs have you been in?",
            options: [
                { text: "A. None — I do research.", score: 1 },
                { text: "B. One or two.", score: 2 },
                { text: "C. More than five.", score: 3 },
                { text: "D. I stopped counting after double digits.", score: 4 }
            ]
        },
        {
            question: "8. How many Discord/TG alpha groups are you in?",
            options: [
                { text: "A. 0–2", score: 1 },
                { text: "B. 3–5", score: 2 },
                { text: "C. 6–10", score: 3 },
                { text: "D. I've muted 30+ servers.", score: 4 }
            ]
        },
        {
            question: "9. Who do you say GM to first?",
            options: [
                { text: "A. My family – priorities, right?", score: 1 },
                { text: "B. My pet – they understand me.", score: 2 },
                { text: "C. Crypto Twitter – first tweet is always \"gm if you gm.\"", score: 3 },
                { text: "D. I don't say GM – I'm nocturnal and only say \"cope.\"", score: 4 }
            ]
        },
        {
            question: "10. How often do you check your PnL or wallet balance?",
            options: [
                { text: "A. Once a day or less — it's long-term.", score: 1 },
                { text: "B. Every few hours.", score: 2 },
                { text: "C. Every 10 minutes, especially during pumps.", score: 3 },
                { text: "D. I don't check — I feel the vibes.", score: 4 }
            ]
        }
    ];

    // Degen levels based on score
    const degenLevels = [
        { min: 0, max: 10, title: "Mild Normie", description: "You value your money, your time, and your mental health. Pathetic." },
        { min: 11, max: 20, title: "Low-Level Degen", description: "Still using stop-losses. Coward." },
        { min: 21, max: 30, title: "Mid-Tier Degen", description: "You're a bot" },
        { min: 31, max: 35, title: "Certified Degen", description: "Your bags are as deep as your coping." },
        { min: 36, max: 40, title: "Degen Royalty", description: "You're probably holding something called $degen" }
    ];

    // DOM elements
    const startBtn = document.getElementById('start-btn');
    const backBtn = document.getElementById('back-btn');
    const scoreDropdownBtn = document.getElementById('score-dropdown-btn');
    const introSection = document.getElementById('intro');
    const quizSection = document.getElementById('quiz');
    const resultsSection = document.getElementById('results');
    const questionContainer = document.getElementById('question-container');
    const optionsContainer = document.getElementById('options-container');
    const progressBar = document.querySelector('.progress');
    const degenLevelEl = document.getElementById('degen-level');
    const degenDescriptionEl = document.getElementById('degen-description');
    const scoreBreakdownEl = document.getElementById('score-breakdown');
    const shareBtn = document.getElementById('share-btn');
    const copyContractBtn = document.getElementById('copy-contract-btn');

    // Quiz state
    let currentQuestionIndex = 0;
    let userScore = 0;
    let userAnswers = [];

    // Start quiz
    startBtn.addEventListener('click', startQuiz);
    
    // Remove restart button event listener since button has been removed
    
    // Back button
    backBtn.addEventListener('click', goToPreviousQuestion);
    
    // Copy contract address
    copyContractBtn.addEventListener('click', function() {
        const contractDisplay = document.getElementById('contract-display');
        navigator.clipboard.writeText(contractDisplay.textContent)
            .then(() => {
                copyContractBtn.textContent = 'COPIED!';
                setTimeout(() => {
                    copyContractBtn.textContent = 'COPY';
                }, 2000);
            })
            .catch(err => {
                console.error('Failed to copy text: ', err);
            });
    });

    function startQuiz() {
        introSection.classList.remove('active');
        quizSection.classList.add('active');
        currentQuestionIndex = 0;
        userScore = 0;
        userAnswers = [];
        showQuestion(currentQuestionIndex);
    }

    // Removed restartQuiz function since it's no longer needed

    function showQuestion(index) {
        // Update progress bar
        const progress = ((index) / questions.length) * 100;
        progressBar.style.width = `${progress}%`;

        // Show question
        const question = questions[index];
        questionContainer.textContent = question.question;

        // Clear previous options
        optionsContainer.innerHTML = '';

        // Add options
        question.options.forEach((option, i) => {
            const optionEl = document.createElement('div');
            optionEl.classList.add('option');
            optionEl.textContent = option.text;
            optionEl.addEventListener('click', () => selectOption(option, index));
            optionsContainer.appendChild(optionEl);
            
            // Mark option as selected if it was previously selected
            if (userAnswers[index] && userAnswers[index].text === option.text) {
                optionEl.classList.add('selected');
            }
        });
        
        // Toggle back button visibility based on question index
        if (index === 0) {
            backBtn.style.display = 'none';
        } else {
            backBtn.style.display = 'block';
        }
    }

    function selectOption(option, questionIndex) {
        // Check if this answer is different from previous selection
        let oldScore = 0;
        if (userAnswers[questionIndex]) {
            oldScore = userAnswers[questionIndex].score;
        }

        // Store answer
        userAnswers[questionIndex] = option;
        userScore = userScore - oldScore + option.score;

        // Highlight selected option
        const options = document.querySelectorAll('.option');
        options.forEach(opt => opt.classList.remove('selected'));
        event.target.classList.add('selected');

        // Go to next question or show results after a short delay
        setTimeout(() => {
            if (currentQuestionIndex < questions.length - 1) {
                currentQuestionIndex++;
                showQuestion(currentQuestionIndex);
            } else {
                showResults();
            }
        }, 500);
    }

    function goToPreviousQuestion() {
        if (currentQuestionIndex > 0) {
            currentQuestionIndex--;
            showQuestion(currentQuestionIndex);
        }
    }

    function showResults() {
        quizSection.classList.remove('active');
        resultsSection.classList.add('active');

        // Find degen level based on score
        const degenLevel = degenLevels.find(level => userScore >= level.min && userScore <= level.max);

        // Display results
        degenLevelEl.textContent = degenLevel.title;
        degenDescriptionEl.textContent = degenLevel.description;

        // Score breakdown
        let breakdownHTML = `<div class="final-score">FINAL SCORE: ${userScore}/40</div><ul class="score-list">`;
        questions.forEach((question, index) => {
            if (userAnswers[index]) {
                breakdownHTML += `<li>${question.question} <span class="answer">${userAnswers[index].text}</span> <span class="points">(${userAnswers[index].score} points)</span></li>`;
            }
        });
        breakdownHTML += '</ul>';
        scoreBreakdownEl.innerHTML = breakdownHTML;
    }

    // Toggle score breakdown dropdown with animation
    scoreDropdownBtn.addEventListener('click', function() {
        const dropdownContent = document.getElementById('score-breakdown');
        const isVisible = dropdownContent.classList.contains('show');
        
        dropdownContent.classList.toggle('show');
        
        // Update button text based on state
        if (isVisible) {
            this.textContent = 'VIEW SCORE DETAILS';
        } else {
            this.textContent = 'HIDE SCORE DETAILS';
        }
    });
    
    // Share results
    shareBtn.addEventListener('click', function() {
        const degenLevel = document.getElementById('degen-level').textContent;
        const degenDescription = document.getElementById('degen-description').textContent;
        
        // Create tweet text
        const tweetText = `Degen Test Results: \n\nI'm a ${degenLevel}\n\nTake the test yourself! #DEGEN #Crypto`;
        
        // Create Twitter share URL
        const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(tweetText)}&url=${encodeURIComponent(window.location.href)}`;
        
        // Open Twitter in a new window
        window.open(twitterUrl, '_blank');
    });
});